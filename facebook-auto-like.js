// ==UserScript==
// @name         Facebook Auto Liker
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  自動為指定的 Facebook 貼文點讚
// <AUTHOR> Name
// @match        https://www.facebook.com/*
// @grant        none
// ==/UserScript==

(function() {
    'use strict';

    // 存储当前状态到 localStorage
    function saveCurrentState(isRunning, originalUrl = '', finalUrl = '') {
        localStorage.setItem('fb_auto_like_state', JSON.stringify({
            isRunning: isRunning,
            originalUrl: originalUrl,
            finalUrl: finalUrl,
            timestamp: new Date().getTime()
        }));
    }

    // 从 localStorage 获取状态
    function getStoredState() {
        const state = localStorage.getItem('fb_auto_like_state');
        if (state) {
            const parsedState = JSON.parse(state);
            if (new Date().getTime() - parsedState.timestamp < 300000) {
                return parsedState;
            }
        }
        return { isRunning: false, originalUrl: '', finalUrl: '' };
    }

    // 初始化运行状态
    const storedState = getStoredState();
    let isRunning = storedState.isRunning;
    let currentOriginalUrl = storedState.originalUrl;
    let currentFinalUrl = storedState.finalUrl;

    // 設定要自動點讚的貼文 URL 列表
    const targetPosts = JSON.parse(localStorage.getItem('fb_auto_like_posts') || '[]');
    // 已點讚的貼文記錄
    const likedPosts = JSON.parse(localStorage.getItem('fb_liked_posts') || '[]');

    // 獲取待點讚的貼文列表
    function getPendingPosts() {
        return targetPosts.filter(url => !likedPosts.includes(url));
    }

    // 清除所有待处理链接
    function clearAllPendingLinks() {
        targetPosts.length = 0;
        localStorage.setItem('fb_auto_like_posts', JSON.stringify(targetPosts));
        logUserAction('清除所有待处理链接');
        updatePendingList();
    }

    // 用户操作日志记录函数
    function logUserAction(message) {
        const timestamp = new Date().toLocaleTimeString();
        console.log(`[${timestamp}] ${message}`);
        
        const logPanel = document.querySelector('#userLogPanel');
        if (logPanel) {
            const logEntry = document.createElement('div');
            logEntry.style.cssText = `
                padding: 5px 0;
                border-bottom: 1px solid #eee;
                font-size: 12px;
            `;
            logEntry.innerHTML = `<span style="color:#999;">[${timestamp}]</span> ${message}`;
            logPanel.insertBefore(logEntry, logPanel.firstChild);
            
            // 限制日志条目数量
            if (logPanel.children.length > 50) {
                logPanel.removeChild(logPanel.lastChild);
            }
        }
    }

    // 清除日志
    function clearLogs() {
        const logPanel = document.querySelector('#userLogPanel');
        if (logPanel) {
            logPanel.innerHTML = '';
        }
    }

    // 创建按钮
    function createButton(text, bgColor, hoverColor) {
        const button = document.createElement('button');
        button.textContent = text;
        button.style.cssText = `
            background: ${bgColor};
            color: white;
            border: none;
            border-radius: 4px;
            padding: 8px 12px;
            margin: 5px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.3s;
        `;
        button.onmouseover = () => button.style.background = hoverColor;
        button.onmouseout = () => button.style.background = bgColor;
        return button;
    }

    // 显示通知
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#4CAF50' : '#F44336'};
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 10000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        `;
        document.body.appendChild(toast);
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transition = 'opacity 0.5s';
            setTimeout(() => document.body.removeChild(toast), 500);
        }, 3000);
    }

    // 解析Facebook链接
    function parseFacebookUrls(text) {
        const urlRegex = /(https?:\/\/(?:www\.|m\.)?facebook\.com\/[^\s]+)/g;
        const matches = text.match(urlRegex);
        return matches ? matches : [];
    }

    // 添加到点赞列表
    function addToLikeList(input) {
        if (!input) return false;

        const urls = parseFacebookUrls(input);
        if (urls.length === 0) {
            alert('未找到有效的 Facebook 链接！');
            return false;
        }

        let addedCount = 0;
        urls.forEach(url => {
            if (!targetPosts.includes(url)) {
                targetPosts.push(url);
                addedCount++;
            }
        });

        if (addedCount > 0) {
            localStorage.setItem('fb_auto_like_posts', JSON.stringify(targetPosts));
            logUserAction(`批量添加 ${addedCount} 个链接到待点赞列表`);
            return true;
        }

        alert('所有链接都已经在列表中！');
        return false;
    }

    // 标记已点赞
    function markAsLiked(url) {
        if (!likedPosts.includes(url)) {
            likedPosts.push(url);
            localStorage.setItem('fb_liked_posts', JSON.stringify(likedPosts));
            logUserAction(`完成点赞并标记: ${url}`);
        }
    }

    // 检查是否已经点赞成功
    function checkLikeSuccess() {
        // 查找"移除赞"按钮
        const removeLikeButton = document.querySelector('[aria-label="移除赞"]');

        // 检查是否有蓝色高亮的点赞按钮（另一种已点赞的指示）
        const highlightedLikeButton = document.querySelector('[aria-label="赞"][role="button"][class*="active"]');
        
        // 检查是否有其他可能的点赞成功指示（如带有特定样式的点赞图标）
        const likeIcon = document.querySelector('svg[aria-label="赞"][fill="#1877F2"]');

        const result = !!removeLikeButton || !!highlightedLikeButton || !!likeIcon;
        console.log('点赞状态检查:', result ? '已点赞' : '未点赞', {
            removeLikeButton: !!removeLikeButton,
            highlightedLikeButton: !!highlightedLikeButton,
            likeIcon: !!likeIcon
        });
        return result;
    }

    // 查找點讚按鈕
    function findLikeButton() {
        const likeButtons = Array.from(document.querySelectorAll('[aria-label="赞"][role="button"]'));
        console.log('找到点赞按钮数量:', likeButtons.length);
        
        // 排除 MainFeed 元素内的按钮 (适用于所有格式)
        const mainFeedElement = document.querySelector('[data-pagelet="MainFeed"]');
        
        if (mainFeedElement && likeButtons.length > 0) {
            // 过滤掉在 MainFeed 元素内的按钮
            const filteredButtons = likeButtons.filter(button => {
                return !mainFeedElement.contains(button);
            });
            
            console.log('排除 MainFeed 内的按钮后剩余数量:', filteredButtons.length);
            
            // 使用第一个不在 MainFeed 内的按钮
            if (filteredButtons.length > 0) {
                console.log('使用不在 MainFeed 内的第一个点赞按钮');
                return filteredButtons[0];
            }
        }
        
        // 如果没有找到合适的按钮，尝试其他选择器
        const alternativeButtons = document.querySelectorAll('[data-testid="UFI2ReactionLink"]');
        if (alternativeButtons.length > 0) {
            console.log('使用替代点赞按钮');
            return alternativeButtons[0];
        }
        
        // 如果还是没有找到，返回第一个点赞按钮（如果有的话）
        if (likeButtons.length > 0) {
            console.log('使用第一个点赞按钮');
            return likeButtons[0];
        }
        
        console.log('未找到任何点赞按钮');
        return null;
    }

    // 執行點讚
    async function performLike() {
        const pendingPosts = getPendingPosts();
        if (pendingPosts.length === 0) {
            logUserAction('所有链接已处理完成');
            alert('所有貼文已點讚完成！');
            stopAutoLike();
            return;
        }

        const nextPost = pendingPosts[0];
        logUserAction(`正在处理链接: ${nextPost}`);

        currentOriginalUrl = nextPost;
        currentFinalUrl = '';  // 重置最终URL
        saveCurrentState(true, nextPost, '');

        // 如果当前页面不是目标链接，则跳转
        if (window.location.href !== nextPost) {
            window.location.href = nextPost;
            return;
        }

        // 如果已在目标页面，直接检查点赞状态
        checkAndLike(nextPost);
    }

    // 检查并点赞
    function checkAndLike(originalUrl) {
        // 确保页面已完全加载
        if (document.readyState !== 'complete') {
            setTimeout(() => checkAndLike(originalUrl), 1000);
            return;
        }

        // 记录当前页面URL
        if (!currentFinalUrl) {
            currentFinalUrl = window.location.href;
            saveCurrentState(true, originalUrl, currentFinalUrl);
            logUserAction(`记录最终URL: ${currentFinalUrl}`);
        }

        // 先检查是否已经点赞
        if (checkLikeSuccess()) {
            logUserAction(`检测到已点赞: ${originalUrl}`);
            markAsLiked(originalUrl);
            currentOriginalUrl = '';
            currentFinalUrl = '';
            saveCurrentState(true, '', '');
            setTimeout(() => {
                if (isRunning) {
                    performLike();
                }
            }, 2000);
            return;
        }

        // 如果未点赞，开始点赞流程
        let retryCount = 0;
        const maxRetries = 5;
        let likeConfirmed = false;

        const tryLike = setInterval(() => {
            if (!isRunning) {
                clearInterval(tryLike);
                return;
            }

            retryCount++;
            console.log(`尝试点赞 (第 ${retryCount} 次)`);

            // 再次检查是否已点赞成功
            if (checkLikeSuccess()) {
                // 增加额外验证：连续两次检测到点赞成功才确认
                if (likeConfirmed) {
                    clearInterval(tryLike);
                    logUserAction(`点赞成功确认: ${originalUrl}`);
                    markAsLiked(originalUrl);
                    currentOriginalUrl = '';
                    currentFinalUrl = '';
                    saveCurrentState(true, '', '');
                    setTimeout(() => {
                        if (isRunning) {
                            performLike();
                        }
                    }, 2000);
                    return;
                } else {
                    // 第一次检测到成功，标记但不立即确认
                    likeConfirmed = true;
                    logUserAction(`初步检测到点赞成功，等待确认: ${originalUrl}`);
                    return;
                }
            } else {
                // 如果检测失败，重置确认标记
                likeConfirmed = false;
            }

            const likeButton = findLikeButton();
            if (likeButton) {
                try {
                    likeButton.click();
                    logUserAction(`点击点赞按钮 (第 ${retryCount} 次)`);

                    // 等待点赞确认
                    setTimeout(() => {
                        if (checkLikeSuccess()) {
                            // 设置初步确认标记
                            likeConfirmed = true;
                            logUserAction(`初步检测到点赞成功，等待确认: ${originalUrl}`);
                        }
                    }, 1500);
                } catch (error) {
                    logUserAction(`点赞操作出错: ${error.message}`);
                }
            }

            // 如果达到最大重试次数，放弃当前链接
            if (retryCount >= maxRetries) {
                clearInterval(tryLike);
                logUserAction(`达到最大重试次数，跳过: ${originalUrl}`);
                currentOriginalUrl = '';
                currentFinalUrl = '';
                saveCurrentState(true, '', '');
                setTimeout(() => {
                    if (isRunning) {
                        performLike();
                    }
                }, 2000);
            }
        }, 3000); // 每3秒尝试一次

        // 设置总体超时
        setTimeout(() => {
            clearInterval(tryLike);
            if (isRunning && !likeConfirmed) {
                logUserAction(`处理超时，跳过: ${originalUrl}`);
                currentOriginalUrl = '';
                currentFinalUrl = '';
                saveCurrentState(true, '', '');
                performLike();
            }
        }, 30000);
    }

    // 開始自動點讚流程
    function startAutoLike() {
        if (isRunning) {
            logUserAction('自动点赞已在运行中');
            return;
        }

        const pendingPosts = getPendingPosts();
        if (pendingPosts.length === 0) {
            logUserAction('没有待处理的链接');
            alert('没有待点赞的链接！');
            return;
        }

        isRunning = true;
        saveCurrentState(true, '', '');
        logUserAction(`开始自动点赞 - 待处理数量: ${pendingPosts.length}`);
        performLike();
    }

    // 停止自動點讚
    function stopAutoLike() {
        isRunning = false;
        currentOriginalUrl = '';
        currentFinalUrl = '';
        saveCurrentState(false, '', '');
        logUserAction('停止自动点赞');
    }

    // 存储公共主页信息
    let publicPages = [];
    let currentPageIndex = 0;
    // 添加已切换过的公共主页记录
    let switchedPages = JSON.parse(localStorage.getItem('fb_switched_pages') || '[]');

    // 获取所有公共主页
    function getAllPublicPages() {
        logUserAction('开始获取所有公共主页');
        
        // 点击右上角个人主页按钮
        const profileButton = document.querySelector('[aria-label="你的个人主页"][role="button"]');
        if (!profileButton) {
            logUserAction('未找到个人主页按钮');
            return false;
        }
        
        profileButton.click();
        logUserAction('已点击个人主页按钮');
        
        // 等待菜单加载并点击"查看所有主页"
        setTimeout(() => {
            const viewAllPagesButton = document.querySelector('[aria-label="查看所有主页"][role="button"]');
            if (!viewAllPagesButton) {
                logUserAction('未找到"查看所有主页"按钮');
                return false;
            }
            
            viewAllPagesButton.click();
            logUserAction('已点击"查看所有主页"按钮');
            
            // 等待页面列表加载
            setTimeout(() => {
                // 获取所有公共主页
                const pageItems = document.querySelectorAll('[role="list"] > [data-visualcompletion="ignore-dynamic"][role="listitem"][style*="padding-left: 8px; padding-right: 8px;"] > [aria-label]');
                
                publicPages = [];
                pageItems.forEach(item => {
                    const pageName = item.getAttribute('aria-label');
                    if (pageName) {
                        publicPages.push({
                            name: pageName,
                            element: item,
                            switched: switchedPages.includes(pageName) // 标记是否已切换过
                        });
                    }
                });
                
                // 记录找到的公共主页，并标记已切换状态
                const pagesInfo = publicPages.map(p => `${p.name}${p.switched ? ' (已切换)' : ''}`);
                logUserAction(`找到 ${publicPages.length} 个公共主页: ${pagesInfo.join(', ')}`);
                
                // 关闭菜单
                document.body.click();
            }, 1000);
        }, 1000);
    }

    // 切换到指定的公共主页
    function switchToPublicPage(pageName) {
        logUserAction(`尝试切换到公共主页: ${pageName}`);
        
        // 检查是否已经切换过
        if (switchedPages.includes(pageName)) {
            logUserAction(`公共主页 ${pageName} 已经切换过，跳过`);
            return false;
        }
        
        // 点击右上角个人主页按钮
        const profileButton = document.querySelector('[aria-label="你的个人主页"][role="button"]');
        if (!profileButton) {
            logUserAction('未找到个人主页按钮');
            return false;
        }
        
        profileButton.click();
        logUserAction('已点击个人主页按钮');
        
        // 等待菜单加载并点击"查看所有主页"
        setTimeout(() => {
            const viewAllPagesButton = document.querySelector('[aria-label="查看所有主页"][role="button"]');
            if (!viewAllPagesButton) {
                logUserAction('未找到"查看所有主页"按钮');
                return false;
            }
            
            viewAllPagesButton.click();
            logUserAction('已点击"查看所有主页"按钮');
            
            // 等待页面列表加载
            setTimeout(() => {
                // 查找指定的公共主页
                const pageItems = document.querySelectorAll('[role="list"] > [data-visualcompletion="ignore-dynamic"][role="listitem"][style*="padding-left: 8px; padding-right: 8px;"] > [aria-label]');
                
                let targetPage = null;
                pageItems.forEach(item => {
                    if (item.getAttribute('aria-label') === pageName) {
                        targetPage = item;
                    }
                });
                
                if (targetPage) {
                    targetPage.click();
                    // 记录已切换的公共主页
                    if (!switchedPages.includes(pageName)) {
                        switchedPages.push(pageName);
                        localStorage.setItem('fb_switched_pages', JSON.stringify(switchedPages));
                    }
                    logUserAction(`成功切换到公共主页: ${pageName} (已记录)`);
                    return true;
                } else {
                    logUserAction(`未找到公共主页: ${pageName}`);
                    // 关闭菜单
                    document.body.click();
                    return false;
                }
            }, 1000);
        }, 1000);
    }

    // 切换到下一个未切换过的公共主页
    function switchToNextPublicPage() {
        if (publicPages.length === 0) {
            getAllPublicPages();
            setTimeout(() => {
                if (publicPages.length > 0) {
                    switchToNextUnswitchedPage();
                }
            }, 3000);
        } else {
            switchToNextUnswitchedPage();
        }
    }

    // 查找并切换到下一个未切换过的公共主页
    function switchToNextUnswitchedPage() {
        // 查找未切换过的公共主页
        const unswitchedPages = publicPages.filter(page => !switchedPages.includes(page.name));
        
        if (unswitchedPages.length > 0) {
            // 有未切换过的公共主页，切换到第一个
            const nextPage = unswitchedPages[0];
            logUserAction(`找到未切换过的公共主页: ${nextPage.name}`);
            switchToPublicPage(nextPage.name);
        } else if (publicPages.length > 0) {
            // 所有公共主页都已切换过，提示用户
            logUserAction('所有公共主页都已切换过');
            showToast('所有公共主页都已切换过', 'info');
            
            // 询问是否重置切换记录
            if (confirm('所有公共主页都已切换过，是否重置切换记录？')) {
                resetSwitchedPagesRecord();
            }
        } else {
            logUserAction('没有找到任何公共主页');
            showToast('没有找到任何公共主页', 'error');
        }
    }

    // 重置已切换公共主页记录
    function resetSwitchedPagesRecord() {
        switchedPages = [];
        localStorage.removeItem('fb_switched_pages');
        
        // 更新公共主页的切换状态
        publicPages.forEach(page => {
            page.switched = false;
        });
        
        logUserAction('已重置公共主页切换记录');
        showToast('已重置公共主页切换记录', 'success');
    }

    // 添加公共主页管理按钮到控制面板
    function addPageManagementButtons(buttonGroup) {
        const getPageButton = createButton('获取公共主页', '#17a2b8', '#138496');
        const switchPageButton = createButton('切换公共主页', '#6610f2', '#5a0cdb');
        const resetSwitchRecordButton = createButton('重置切换记录', '#fd7e14', '#e96b02');
        
        getPageButton.onclick = getAllPublicPages;
        switchPageButton.onclick = switchToNextPublicPage;
        resetSwitchRecordButton.onclick = () => {
            if (confirm('确定要重置所有公共主页的切换记录吗？')) {
                resetSwitchedPagesRecord();
            }
        };
        
        buttonGroup.appendChild(getPageButton);
        buttonGroup.appendChild(switchPageButton);
        buttonGroup.appendChild(resetSwitchRecordButton);
    }

    // 添加控制面板
    function addControlPanel() {
        // 主面板
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-width: 320px;
            font-family: Arial, sans-serif;
            transition: all 0.3s ease;
        `;

        // 标题栏
        const titleBar = document.createElement('div');
        titleBar.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        `;

        const title = document.createElement('div');
        title.textContent = 'Facebook 自动点赞';
        title.style.cssText = `
            font-weight: bold;
            font-size: 16px;
            color: #1877f2;
        `;

        // 最小化按钮
        const minimizeBtn = document.createElement('button');
        minimizeBtn.innerHTML = '−';
        minimizeBtn.style.cssText = `
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
            padding: 0 5px;
            line-height: 1;
        `;

        let isMinimized = false;
        const content = document.createElement('div');
        content.style.transition = 'all 0.3s ease';

        minimizeBtn.onclick = () => {
            isMinimized = !isMinimized;
            content.style.display = isMinimized ? 'none' : 'block';
            minimizeBtn.innerHTML = isMinimized ? '+' : '−';
            panel.style.width = isMinimized ? 'auto' : '320px';
        };

        titleBar.appendChild(title);
        titleBar.appendChild(minimizeBtn);
        panel.appendChild(titleBar);

        // 创建带标签的区域函数
        function createLabeledSection(labelText) {
            const section = document.createElement('div');
            section.style.marginBottom = '15px';

            const label = document.createElement('label');
            label.textContent = labelText;
            label.style.cssText = `
                display: block;
                margin-bottom: 5px;
                font-weight: 500;
                color: #444;
                font-size: 14px;
            `;

            section.appendChild(label);
            return section;
        }

        // 链接输入区域
        const inputSection = createLabeledSection('批量添加链接');
        const input = document.createElement('textarea');
        input.placeholder = '输入 Facebook 链接（每行一个）';
        input.style.cssText = `
            width: 100%;
            height: 80px;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            resize: vertical;
            font-size: 14px;
            box-sizing: border-box;
        `;
        inputSection.appendChild(input);

        // 文件上传区域
        const uploadSection = createLabeledSection('文件上传');
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.txt';
        fileInput.style.display = 'none';

        const dropZone = document.createElement('div');
        dropZone.style.cssText = `
            border: 2px dashed #1877f2;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: #f0f2f5;
            cursor: pointer;
            transition: all 0.3s ease;
        `;
        dropZone.innerHTML = `
            <div style="color: #1877f2; margin-bottom: 5px;">
                <i class="fas fa-cloud-upload-alt" style="font-size: 20px;"></i>
            </div>
            <div style="color: #666; font-size: 13px;">
                点击或拖放文件到这里上传<br>
                <span style="font-size: 12px; color: #999;">支持 .txt 文件</span>
            </div>
        `;

        // 处理文件内容的函数
        function handleFileContent(content) {
            if (content.trim()) {
                if (addToLikeList(content)) {
                    const urls = parseFacebookUrls(content);
                    showToast(`成功从文件添加 ${urls.length} 个链接！`);
                    updatePendingList();
                }
            } else {
                showToast('文件内容为空！', 'error');
            }
        }

        // 处理文件的函数
        function handleFile(file) {
            if (file.type !== 'text/plain') {
                showToast('请上传 .txt 文件！', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                handleFileContent(e.target.result);
            };
            reader.onerror = () => {
                showToast('读取文件失败！', 'error');
            };
            reader.readAsText(file);
        }

        // 点击上传
        dropZone.onclick = () => fileInput.click();

        // 文件选择处理
        fileInput.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
            // 清除选择，这样同一个文件可以重复上传
            fileInput.value = '';
        };

        // 拖拽相关事件
        dropZone.ondragover = (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#28a745';
            dropZone.style.background = '#e8f5e9';
        };

        dropZone.ondragleave = (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#1877f2';
            dropZone.style.background = '#f0f2f5';
        };

        dropZone.ondrop = (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#1877f2';
            dropZone.style.background = '#f0f2f5';

            const file = e.dataTransfer.files[0];
            if (file) {
                handleFile(file);
            }
        };

        uploadSection.appendChild(dropZone);
        uploadSection.appendChild(fileInput);

        // 待处理链接列表区域
        const pendingSection = createLabeledSection('待处理链接');
        const pendingHeader = document.createElement('div');
        pendingHeader.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        `;

        // 添加清除所有链接的按钮
        const clearAllButton = document.createElement('button');
        clearAllButton.textContent = '清除所有链接';
        clearAllButton.style.cssText = `
            background: none;
            border: none;
            color: #dc3545;
            cursor: pointer;
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 4px;
            &:hover {
                background: #dc3545;
                color: white;
            }
        `;
        clearAllButton.onclick = () => {
            if (confirm('确定要清除所有链接吗？此操作不可恢复。')) {
                clearAllLinks();
                updatePendingList();
            }
        };

        pendingHeader.appendChild(clearAllButton);
        pendingSection.appendChild(pendingHeader);

        const pendingList = document.createElement('div');
        pendingList.style.cssText = `
            max-height: 150px;
            overflow-y: auto;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fff;
            font-size: 13px;
        `;

        // 更新待处理列表的函数
        function updatePendingList() {
            const pendingPosts = getPendingPosts();
            pendingList.innerHTML = '';
            pendingPosts.forEach(url => {
                const item = document.createElement('div');
                item.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 5px;
                    border-bottom: 1px solid #eee;
                `;

                const urlText = document.createElement('div');
                urlText.style.cssText = `
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    margin-right: 10px;
                `;
                urlText.textContent = url;

                const deleteBtn = document.createElement('button');
                deleteBtn.innerHTML = '×';
                deleteBtn.style.cssText = `
                    background: none;
                    border: none;
                    color: #dc3545;
                    cursor: pointer;
                    font-size: 18px;
                    padding: 0 5px;
                `;
                deleteBtn.onclick = (e) => {
                    e.stopPropagation();
                    const index = targetPosts.indexOf(url);
                    if (index > -1) {
                        targetPosts.splice(index, 1);
                        localStorage.setItem('fb_auto_like_posts', JSON.stringify(targetPosts));
                        updatePendingList();
                        logUserAction(`删除链接: ${url}`);
                    }
                };

                item.appendChild(urlText);
                item.appendChild(deleteBtn);
                pendingList.appendChild(item);
            });
        }
        pendingSection.appendChild(pendingList);

        // 操作日志面板
        const logSection = createLabeledSection('操作日志');
        const logPanel = document.createElement('div');
        logPanel.id = 'userLogPanel';
        logPanel.style.cssText = `
            height: 150px;
            overflow-y: auto;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fff;
            font-size: 13px;
            margin-bottom: 15px;
        `;
        logSection.appendChild(logPanel);

        // 按钮组
        const buttonGroup = document.createElement('div');
        buttonGroup.style.cssText = `
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-top: 10px;
        `;

        function createButton(text, bgColor, hoverColor) {
            const button = document.createElement('button');
            button.textContent = text;
            button.style.cssText = `
                background: ${bgColor};
                color: white;
                border: none;
                border-radius: 4px;
                padding: 8px 12px;
                margin: 5px;
                cursor: pointer;
                font-size: 14px;
                transition: background 0.3s;
            `;
            button.onmouseover = () => button.style.background = hoverColor;
            button.onmouseout = () => button.style.background = bgColor;
            return button;
        }

        const startButton = createButton('开始点赞', '#1877f2', '#0d65d9');
        const stopButton = createButton('停止点赞', '#dc3545', '#c82333');
        const clearLogsButton = createButton('清除日志', '#6c757d', '#5a6268');
        const addButton = createButton('添加链接', '#28a745', '#218838');

        startButton.onclick = startAutoLike;
        stopButton.onclick = stopAutoLike;
        clearLogsButton.onclick = () => {
            if (confirm('确定要清除所有日志记录吗？')) {
                clearLogs();
            }
        };
        addButton.onclick = () => {
            if (addToLikeList(input.value)) {
                const urls = parseFacebookUrls(input.value);
                showToast(`成功添加 ${urls.length} 个链接！`);
                input.value = '';
                updatePendingList();
            }
        };

        // 添加公共主页管理按钮
        addPageManagementButtons(buttonGroup);

        // 添加所有元素到内容区
        content.appendChild(inputSection);
        content.appendChild(uploadSection);
        content.appendChild(pendingSection);
        content.appendChild(logSection);
        buttonGroup.appendChild(startButton);
        buttonGroup.appendChild(stopButton);
        buttonGroup.appendChild(clearLogsButton);
        buttonGroup.appendChild(addButton);
        content.appendChild(buttonGroup);

        panel.appendChild(content);
        document.body.appendChild(panel);

        // 初始化显示
        updatePendingList();
    }

    // 添加提示框功能
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: ${type === 'success' ? '#4CAF50' : '#F44336'};
            color: white;
            padding: 10px 20px;
            border-radius: 4px;
            z-index: 10000;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        `;
        document.body.appendChild(toast);
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transition = 'opacity 0.5s';
            setTimeout(() => document.body.removeChild(toast), 500);
        }, 3000);
    }

    // 添加必要的动画样式
    const style1 = document.createElement('style');
    style1.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style1);

    // 页面加载时检查是否需要继续任务
    window.addEventListener('load', function() {
        if (isRunning) {
            const currentUrl = window.location.href;

            if (currentOriginalUrl) {
                // 如果有正在处理的原始URL
                if (!currentFinalUrl) {
                    // 如果还没有记录最终URL，说明这是重定向后的页面
                    logUserAction(`处理重定向后的页面: ${currentUrl}`);
                    checkAndLike(currentOriginalUrl);
                } else if (currentUrl === currentFinalUrl) {
                    // 如果当前URL与记录的最终URL匹配
                    logUserAction(`继续处理当前页面: ${currentUrl}`);
                    checkAndLike(currentOriginalUrl);
                } else {
                    // URL不匹配，可能是用户手动导航，重新开始处理
                    performLike();
                }
            } else {
                // 没有正在处理的URL，开始处理新的链接
                performLike();
            }
        }
    });

    // 初始化时如果正在运行，则继续执行
    if (isRunning) {
        window.addEventListener('load', function() {
            logUserAction('恢复自动点赞任务');
            const currentUrl = window.location.href;
            checkAndLike(currentUrl);
        });
    }

    // 初始化
    addControlPanel();
    console.log('Facebook Auto Liker 已啟動');
})();










