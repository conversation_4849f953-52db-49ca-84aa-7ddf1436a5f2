// ==UserScript==
// @name         Facebook Auto Liker
// @namespace    http://tampermonkey.net/
// @version      0.2
// @description  自動為指定的 Facebook 貼文點讚 (支持浏览器最小化后台运行)
// <AUTHOR> Name
// @match        https://www.facebook.com/*
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_registerMenuCommand
// @grant        GM_notification
// @grant        GM_addValueChangeListener
// @run-at       document-start
// ==/UserScript==

(function() {
    'use strict';

    // 脚本版本和唯一标识
    const SCRIPT_VERSION = '0.2';
    const SCRIPT_ID = 'fb_auto_liker_' + SCRIPT_VERSION;

    // 后台保活机制
    let keepAliveInterval = null;
    let isMainInstance = false;
    let instanceId = 'instance_' + Math.random().toString(36).substring(2, 15);

    // 页面可见性状态管理
    let isPageVisible = !document.hidden;
    let lastActivityTime = Date.now();

    // 初始化跨标签页通信
    function initCrossTabCommunication() {
        // 注册值变化监听器 (Tampermonkey API)
        if (typeof GM_addValueChangeListener !== 'undefined') {
            GM_addValueChangeListener('fb_auto_like_command', function(name, old_value, new_value, remote) {
                if (remote) {
                    console.log('收到跨标签页命令:', new_value);
                    handleCrossTabCommand(new_value);
                }
            });

            console.log('已启用跨标签页通信');
        }
    }

    // 处理跨标签页命令
    function handleCrossTabCommand(command) {
        if (!command) return;

        switch (command.action) {
            case 'ping':
                // 响应ping请求
                sendCrossTabCommand('pong', {
                    respondTo: command.instanceId,
                    instanceId: instanceId,
                    isRunning: isRunning,
                    isVisible: isPageVisible,
                    timestamp: Date.now()
                });
                break;

            case 'pong':
                // 处理pong响应
                if (command.respondTo === instanceId) {
                    console.log('收到pong响应:', command);
                    // 如果有其他实例响应且比当前实例更适合作为主实例，则放弃主实例角色
                    if (command.isVisible && !isPageVisible) {
                        isMainInstance = false;
                        console.log('让位给可见实例作为主实例');
                    }
                }
                break;

            case 'startLike':
                // 远程启动点赞
                if (isRunning) return;
                logUserAction('收到远程启动点赞命令');
                startAutoLike();
                break;

            case 'stopLike':
                // 远程停止点赞
                if (!isRunning) return;
                logUserAction('收到远程停止点赞命令');
                stopAutoLike();
                break;

            case 'heartbeat':
                // 处理心跳
                lastActivityTime = Date.now();
                break;
        }
    }

    // 发送跨标签页命令
    function sendCrossTabCommand(action, data = {}) {
        if (typeof GM_setValue !== 'undefined') {
            const command = {
                action: action,
                instanceId: instanceId,
                timestamp: Date.now(),
                ...data
            };

            GM_setValue('fb_auto_like_command', command);
        }
    }

    // 启动后台保活
    function startKeepAlive() {
        if (keepAliveInterval) return;

        // 确定是否为主实例
        determineMainInstance();

        // 设置保活间隔
        keepAliveInterval = setInterval(() => {
            if (isRunning) {
                // 发送心跳
                sendCrossTabCommand('heartbeat', { isRunning, isVisible: isPageVisible });

                // 如果是主实例，确保任务继续运行
                if (isMainInstance) {
                    console.log('主实例心跳 - 确保任务继续运行');
                    checkTaskContinuity();
                }
            }
        }, 5000); // 每5秒发送一次心跳

        console.log('后台保活机制已启动');
    }

    // 停止后台保活
    function stopKeepAlive() {
        if (keepAliveInterval) {
            clearInterval(keepAliveInterval);
            keepAliveInterval = null;
            isMainInstance = false;
            console.log('后台保活机制已停止');
        }
    }

    // 确定是否为主实例
    function determineMainInstance() {
        // 发送ping请求
        sendCrossTabCommand('ping');

        // 默认情况下，假设自己是主实例
        setTimeout(() => {
            if (isPageVisible) {
                isMainInstance = true;
                console.log('确定为主实例 (可见)');
            } else {
                // 如果页面不可见，只有在没有其他可见实例的情况下才成为主实例
                isMainInstance = true;
                console.log('确定为主实例 (后台)');
            }
        }, 1000);
    }

    // 监听页面可见性变化
    document.addEventListener('visibilitychange', function() {
        isPageVisible = !document.hidden;
        logUserAction(`页面可见性变化: ${isPageVisible ? '可见' : '隐藏'}`);

        if (isPageVisible) {
            // 页面变为可见时，检查是否需要恢复任务
            if (isRunning && Date.now() - lastActivityTime > 10000) {
                logUserAction('页面重新可见，检查任务状态');
                checkTaskContinuity();
            }
        }
    });

    // 使用Tampermonkey的GM函数存储状态，确保跨标签页和后台运行
    function saveCurrentState(isRunning, originalUrl = '', finalUrl = '') {
        const state = {
            isRunning: isRunning,
            originalUrl: originalUrl,
            finalUrl: finalUrl,
            timestamp: new Date().getTime(),
            lastActivityTime: lastActivityTime,
            pageVisible: isPageVisible
        };

        try {
            // 优先使用GM_setValue (Tampermonkey API)
            if (typeof GM_setValue !== 'undefined') {
                GM_setValue('fb_auto_like_state', state);
                console.log('使用GM_setValue保存状态');
            } else {
                // 回退到localStorage
                localStorage.setItem('fb_auto_like_state', JSON.stringify(state));
                console.log('使用localStorage保存状态');
            }
        } catch (error) {
            console.error('保存状态失败:', error);
            // 最后的回退方案
            localStorage.setItem('fb_auto_like_state', JSON.stringify(state));
        }
    }

    // 获取存储的状态
    function getStoredState() {
        try {
            // 优先使用GM_getValue (Tampermonkey API)
            if (typeof GM_getValue !== 'undefined') {
                const state = GM_getValue('fb_auto_like_state');
                console.log('使用GM_getValue获取状态');
                if (state && new Date().getTime() - state.timestamp < 300000) {
                    return state;
                }
            } else {
                // 回退到localStorage
                console.log('使用localStorage获取状态');
                const stateStr = localStorage.getItem('fb_auto_like_state');
                if (stateStr) {
                    const parsedState = JSON.parse(stateStr);
                    if (new Date().getTime() - parsedState.timestamp < 300000) {
                        return parsedState;
                    }
                }
            }
        } catch (error) {
            console.error('获取状态失败:', error);
        }

        return { isRunning: false, originalUrl: '', finalUrl: '', lastActivityTime: Date.now(), pageVisible: true };
    }

    // 检查任务连续性
    function checkTaskContinuity() {
        if (!isRunning) return;

        const currentUrl = window.location.href;
        logUserAction(`检查任务连续性 - 当前URL: ${currentUrl}`);

        if (currentOriginalUrl) {
            // 如果有正在处理的任务，继续处理
            if (!currentFinalUrl || currentUrl === currentFinalUrl) {
                logUserAction('继续处理当前任务');
                checkAndLike(currentOriginalUrl);
            } else {
                logUserAction('URL不匹配，重新开始处理');
                performLike();
            }
        } else {
            // 没有正在处理的任务，开始新任务
            logUserAction('开始处理新任务');
            performLike();
        }
    }

    // 强化的定时器函数，确保在后台也能运行
    function createRobustTimer(callback, interval, maxRetries = 3) {
        let retryCount = 0;
        let timerId;
        let workerTimerId = null;
        let lastExecutionTime = Date.now();

        // 创建Web Worker来确保后台执行
        let timerWorker = null;

        try {
            // 创建内联Web Worker
            const workerBlob = new Blob([`
                let timerId = null;

                self.onmessage = function(e) {
                    if (e.data.command === 'start') {
                        // 启动定时器
                        if (timerId) clearInterval(timerId);
                        timerId = setInterval(() => {
                            self.postMessage({type: 'tick', timestamp: Date.now()});
                        }, ${Math.max(interval, 1000)});

                        self.postMessage({type: 'started'});
                    } else if (e.data.command === 'stop') {
                        // 停止定时器
                        if (timerId) {
                            clearInterval(timerId);
                            timerId = null;
                        }
                        self.postMessage({type: 'stopped'});
                    }
                };
            `], { type: 'application/javascript' });

            const workerUrl = URL.createObjectURL(workerBlob);
            timerWorker = new Worker(workerUrl);

            // 监听Worker消息
            timerWorker.onmessage = function(e) {
                if (e.data.type === 'tick') {
                    // 检查是否应该执行回调
                    const now = Date.now();
                    if (now - lastExecutionTime >= interval) {
                        lastExecutionTime = now;
                        executeCallback();
                    }
                }
            };

            // 启动Worker定时器
            timerWorker.postMessage({command: 'start'});
            console.log('Web Worker定时器已启动');
        } catch (error) {
            console.error('创建Web Worker失败，回退到标准定时器', error);
            timerWorker = null;
        }

        function executeCallback() {
            try {
                lastActivityTime = Date.now();
                callback();
                retryCount = 0; // 重置重试计数
            } catch (error) {
                logUserAction(`定时器执行出错: ${error.message}`);
                retryCount++;
                if (retryCount >= maxRetries) {
                    logUserAction(`定时器达到最大重试次数，停止执行`);
                    return;
                }
            }
        }

        function scheduleNext() {
            // 如果Worker可用，则不需要使用这个定时器
            if (timerWorker) return;

            // 使用多种定时器策略的组合来确保在后台也能运行
            if (isPageVisible) {
                // 页面可见时使用正常间隔
                timerId = setTimeout(() => {
                    executeCallback();
                    if (isRunning) scheduleNext();
                }, interval);
            } else {
                // 页面不可见时使用Web Animation API或requestAnimationFrame来保持活跃
                try {
                    // 尝试使用Web Animation API
                    const animation = document.createElement('div').animate(
                        [{ opacity: 0 }, { opacity: 0 }],
                        { duration: interval, iterations: 1 }
                    );

                    animation.onfinish = () => {
                        executeCallback();
                        if (isRunning) scheduleNext();
                    };
                } catch (error) {
                    // 回退到setTimeout
                    timerId = setTimeout(() => {
                        executeCallback();
                        if (isRunning) scheduleNext();
                    }, Math.max(interval, 1000)); // 最小1秒间隔
                }
            }
        }

        // 如果Worker不可用，则使用传统方法
        if (!timerWorker) {
            scheduleNext();
        }

        return {
            clear: () => {
                if (timerId) {
                    clearTimeout(timerId);
                    timerId = null;
                }

                if (timerWorker) {
                    timerWorker.postMessage({command: 'stop'});
                    timerWorker.terminate();
                    timerWorker = null;
                }
            }
        };
    }

    // 初始化运行状态
    const storedState = getStoredState();
    let isRunning = storedState.isRunning;
    let currentOriginalUrl = storedState.originalUrl;
    let currentFinalUrl = storedState.finalUrl;

    // 設定要自動點讚的貼文 URL 列表
    const targetPosts = JSON.parse(localStorage.getItem('fb_auto_like_posts') || '[]');
    // 已點讚的貼文記錄
    const likedPosts = JSON.parse(localStorage.getItem('fb_liked_posts') || '[]');

    // 獲取待點讚的貼文列表
    function getPendingPosts() {
        return targetPosts.filter(url => !likedPosts.includes(url));
    }

    // 更新顯示待點讚列表
    function updatePendingList(listElement) {
        const pendingPosts = getPendingPosts();
        listElement.innerHTML = '';
        pendingPosts.forEach(url => {
            const item = document.createElement('div');
            item.textContent = url;
            item.style.marginBottom = '5px';
            listElement.appendChild(item);
        });
    }

    // 用户操作日志记录函数
    function logUserAction(action) {
        const logPanel = document.querySelector('#userLogPanel');
        if (logPanel) {
            const logEntry = document.createElement('div');
            logEntry.style.borderBottom = '1px solid #eee';
            logEntry.style.padding = '2px 0';
            logEntry.textContent = `${new Date().toLocaleTimeString()} - ${action}`;
            logPanel.appendChild(logEntry);
            logPanel.scrollTop = logPanel.scrollHeight;

            // 保持最多显示50条记录
            while (logPanel.children.length > 50) {
                logPanel.removeChild(logPanel.firstChild);
            }
        }
    }

    // 解析输入文本中的 Facebook 链接
    function parseFacebookUrls(text) {
        // Facebook URL 的正则表达式模式
        const fbPatterns = [
            // 标准分享链接格式 (确保ID至少有5个字符)
            /https:\/\/www\.facebook\.com\/share\/[a-zA-Z0-9]{5,}(?:\/)?/g,
            // 带 p 的分享链接格式 (确保ID至少有5个字符)
            /https:\/\/www\.facebook\.com\/share\/p\/[a-zA-Z0-9]{5,}(?:\/)?/g,
            // 带 v 的分享链接格式 (确保ID至少有5个字符)
            /https:\/\/www\.facebook\.com\/share\/v\/[a-zA-Z0-9]{5,}(?:\/)?/g,
            // 其他可能的分享链接格式
            /https:\/\/www\.facebook\.com\/[a-zA-Z0-9.]+\/posts\/[a-zA-Z0-9]{5,}(?:\/)?/g,
            /https:\/\/www\.facebook\.com\/permalink\.php\?story_fbid=[a-zA-Z0-9]{5,}(?:&[^&]*)*$/g
        ];

        // 存储所有匹配的链接
        let allUrls = [];

        // 使用每个模式匹配链接
        fbPatterns.forEach(pattern => {
            const matches = text.match(pattern) || [];
            allUrls = allUrls.concat(matches);
        });

        // 规范化链接格式并验证
        const normalizedUrls = allUrls
            .map(url => {
                // 移除链接末尾的查询参数
                url = url.split('?')[0];
                // 确保链接末尾有 /
                return url.endsWith('/') ? url : `${url}/`;
            })
            .filter(url => {
                // 验证链接是否包含有效的ID
                const hasValidId = url.match(/[a-zA-Z0-9]{5,}/);
                // 验证链接不是仅包含基本路径
                const isNotBasePath = !url.match(/\/share\/$/) && !url.match(/\/share\/p\/$/);
                return hasValidId && isNotBasePath;
            });

        // 去重
        const uniqueUrls = [...new Set(normalizedUrls)];

        console.log(`解析到 ${uniqueUrls.length} 个有效的分享链接`);
        return uniqueUrls;
    }

    // 添加到点赞列表
    function addToLikeList(input) {
        if (!input) return false;

        const urls = parseFacebookUrls(input);
        if (urls.length === 0) {
            alert('未找到有效的 Facebook 链接！');
            return false;
        }

        let addedCount = 0;
        urls.forEach(url => {
            if (!targetPosts.includes(url)) {
                targetPosts.push(url);
                addedCount++;
            }
        });

        if (addedCount > 0) {
            localStorage.setItem('fb_auto_like_posts', JSON.stringify(targetPosts));
            logUserAction(`批量添加 ${addedCount} 个链接到待点赞列表`);
            return true;
        }

        alert('所有链接都已经在列表中！');
        return false;
    }

    // 标记已点赞
    function markAsLiked(url) {
        if (!likedPosts.includes(url)) {
            likedPosts.push(url);
            localStorage.setItem('fb_liked_posts', JSON.stringify(likedPosts));
            logUserAction(`完成点赞并标记: ${url}`);
        }
    }

    // 检查是否已经点赞成功
    function checkLikeSuccess() {
        // 查找"移除赞"按钮
        const removeLikeButton = document.querySelector('[aria-label="移除赞"]');

        // 检查是否有蓝色高亮的点赞按钮（另一种已点赞的指示）
        const highlightedLikeButton = document.querySelector('[aria-label="赞"][role="button"][class*="active"]');

        // 检查是否有其他可能的点赞成功指示（如带有特定样式的点赞图标）
        const likeIcon = document.querySelector('svg[aria-label="赞"][fill="#1877F2"]');

        const result = !!removeLikeButton || !!highlightedLikeButton || !!likeIcon;
        console.log('点赞状态检查:', result ? '已点赞' : '未点赞', {
            removeLikeButton: !!removeLikeButton,
            highlightedLikeButton: !!highlightedLikeButton,
            likeIcon: !!likeIcon
        });
        return result;
    }

    // 查找點讚按鈕
    function findLikeButton() {
        const likeButtons = Array.from(document.querySelectorAll('[aria-label="赞"][role="button"]'));
        console.log(`找到点赞按钮数量: ${likeButtons.length} - 页面状态: ${isPageVisible ? '可见' : '后台'}`);
        logUserAction(`DOM查询: 找到 ${likeButtons.length} 个点赞按钮 - 页面状态: ${isPageVisible ? '可见' : '后台'}`);

        // 排除 MainFeed 元素内的按钮 (适用于所有格式)
        const mainFeedElement = document.querySelector('[data-pagelet="MainFeed"]');

        if (mainFeedElement && likeButtons.length > 0) {
            // 过滤掉在 MainFeed 元素内的按钮
            const filteredButtons = likeButtons.filter(button => {
                return !mainFeedElement.contains(button);
            });

            console.log(`排除 MainFeed 内的按钮后剩余数量: ${filteredButtons.length}`);
            logUserAction(`过滤后剩余 ${filteredButtons.length} 个可用按钮`);

            // 使用第一个不在 MainFeed 内的按钮
            if (filteredButtons.length > 0) {
                console.log('使用不在 MainFeed 内的第一个点赞按钮');
                logUserAction('选择第一个可用的点赞按钮');
                return filteredButtons[0];
            }
        }

        // 如果没有找到符合条件的按钮或没有 MainFeed 元素，使用默认逻辑
        if (likeButtons.length >= 2) {
            console.log('使用第二个点赞按钮');
            logUserAction('使用第二个点赞按钮');
            return likeButtons[1];
        } else if (likeButtons.length === 1) {
            console.log('只找到一个点赞按钮');
            logUserAction('使用唯一的点赞按钮');
            return likeButtons[0];
        }

        console.log('未找到点赞按钮');
        logUserAction('未找到可用的点赞按钮');
        return null;
    }

    // 執行點讚
    async function performLike() {
        const pendingPosts = getPendingPosts();
        if (pendingPosts.length === 0) {
            logUserAction('所有链接已处理完成');
            // 清除所有待处理链接
            clearAllPendingLinks();
            alert('所有貼文已點讚完成！');
            stopAutoLike();
            return;
        }

        const nextPost = pendingPosts[0];
        logUserAction(`正在处理链接: ${nextPost}`);

        currentOriginalUrl = nextPost;
        currentFinalUrl = '';  // 重置最终URL
        saveCurrentState(true, nextPost, '');

        // 如果当前页面不是目标链接，则跳转
        if (window.location.href !== nextPost) {
            window.location.href = nextPost;
            return;
        }

        // 如果已在目标页面，直接检查点赞状态
        checkAndLike(nextPost);
    }

    // 新增清除待处理链接的函数
    function clearAllPendingLinks() {
        // 清除内存中的数组
        targetPosts.length = 0;

        // 清除本地存储中的待处理链接
        localStorage.removeItem('fb_auto_like_posts');

        // 更新显示
        const pendingList = document.querySelector('#pendingList');
        if (pendingList) {
            pendingList.innerHTML = '';
        }

        logUserAction('清除所有待处理链接');
    }

    // 检查并点赞
    function checkAndLike(originalUrl) {
        // 确保页面已完全加载
        if (document.readyState !== 'complete') {
            setTimeout(() => checkAndLike(originalUrl), 1000);
            return;
        }

        // 记录当前页面URL
        if (!currentFinalUrl) {
            currentFinalUrl = window.location.href;
            saveCurrentState(true, originalUrl, currentFinalUrl);
            logUserAction(`记录最终URL: ${currentFinalUrl}`);
        }

        // 先检查是否已经点赞
        if (checkLikeSuccess()) {
            logUserAction(`检测到已点赞: ${originalUrl}`);
            markAsLiked(originalUrl);
            currentOriginalUrl = '';
            currentFinalUrl = '';
            saveCurrentState(true, '', '');
            setTimeout(() => {
                if (isRunning) {
                    performLike();
                }
            }, 2000);
            return;
        }

        // 如果未点赞，开始点赞流程
        let retryCount = 0;
        const maxRetries = 5;
        let likeConfirmed = false;
        let robustTimer = null;

        // 使用强化定时器进行点赞尝试
        function attemptLike() {
            if (!isRunning) {
                if (robustTimer) robustTimer.clear();
                return;
            }

            retryCount++;
            console.log(`尝试点赞 (第 ${retryCount} 次) - 页面可见性: ${isPageVisible}`);
            logUserAction(`尝试点赞 (第 ${retryCount} 次) - 页面状态: ${isPageVisible ? '可见' : '后台'}`);

            // 再次检查是否已点赞成功
            if (checkLikeSuccess()) {
                // 增加额外验证：连续两次检测到点赞成功才确认
                if (likeConfirmed) {
                    if (robustTimer) robustTimer.clear();
                    logUserAction(`点赞成功确认: ${originalUrl}`);
                    markAsLiked(originalUrl);
                    currentOriginalUrl = '';
                    currentFinalUrl = '';
                    saveCurrentState(true, '', '');
                    setTimeout(() => {
                        if (isRunning) {
                            performLike();
                        }
                    }, 2000);
                    return;
                } else {
                    // 第一次检测到成功，标记但不立即确认
                    likeConfirmed = true;
                    logUserAction(`初步检测到点赞成功，等待确认: ${originalUrl}`);
                    return;
                }
            } else {
                // 如果检测失败，重置确认标记
                likeConfirmed = false;
            }

            const likeButton = findLikeButton();
            if (likeButton) {
                try {
                    likeButton.click();
                    logUserAction(`点击点赞按钮 (第 ${retryCount} 次) - 后台模式: ${!isPageVisible}`);

                    // 等待点赞确认
                    setTimeout(() => {
                        if (checkLikeSuccess()) {
                            // 设置初步确认标记
                            likeConfirmed = true;
                            logUserAction(`初步检测到点赞成功，等待确认: ${originalUrl}`);
                        }
                    }, 1500);
                } catch (error) {
                    logUserAction(`点赞操作出错: ${error.message}`);
                }
            }

            // 如果达到最大重试次数，放弃当前链接
            if (retryCount >= maxRetries) {
                if (robustTimer) robustTimer.clear();
                logUserAction(`达到最大重试次数，跳过: ${originalUrl}`);
                currentOriginalUrl = '';
                currentFinalUrl = '';
                saveCurrentState(true, '', '');
                setTimeout(() => {
                    if (isRunning) {
                        performLike();
                    }
                }, 2000);
            }
        }

        // 启动强化定时器
        robustTimer = createRobustTimer(attemptLike, 3000);

        // 设置总体超时
        setTimeout(() => {
            if (robustTimer) robustTimer.clear();
            if (isRunning && !likeConfirmed) {
                logUserAction(`处理超时，跳过: ${originalUrl}`);
                currentOriginalUrl = '';
                currentFinalUrl = '';
                saveCurrentState(true, '', '');
                performLike();
            }
        }, 30000);
    }

    // 開始自動點讚流程
    function startAutoLike() {
        if (isRunning) {
            logUserAction('自动点赞已在运行中');
            return;
        }

        const pendingPosts = getPendingPosts();
        if (pendingPosts.length === 0) {
            logUserAction('没有待处理的链接');
            alert('没有待点赞的链接！');
            return;
        }

        isRunning = true;
        lastActivityTime = Date.now();
        saveCurrentState(true, '', '');

        // 启动心跳和保活机制
        startHeartbeat();
        startKeepAlive();

        // 发送跨标签页通知
        sendCrossTabCommand('startLike');

        // 显示通知
        if (typeof GM_notification !== 'undefined') {
            GM_notification({
                title: 'Facebook 自动点赞',
                text: `开始处理 ${pendingPosts.length} 个链接 (支持后台运行)`,
                timeout: 3000
            });
        }

        logUserAction(`开始自动点赞 - 待处理数量: ${pendingPosts.length} - 后台运行支持: 已启用 - 实例ID: ${instanceId}`);
        performLike();
    }

    // 停止自動點讚
    function stopAutoLike() {
        isRunning = false;
        currentOriginalUrl = '';
        currentFinalUrl = '';

        // 停止心跳和保活机制
        stopHeartbeat();
        stopKeepAlive();

        // 发送跨标签页通知
        sendCrossTabCommand('stopLike');

        saveCurrentState(false, '', '');
        logUserAction('停止自动点赞 - 后台运行支持: 已停用');

        // 显示通知
        if (typeof GM_notification !== 'undefined') {
            GM_notification({
                title: 'Facebook 自动点赞',
                text: '已停止自动点赞',
                timeout: 2000
            });
        }
    }

    // 清除点赞记录
    function clearLikeHistory() {
        localStorage.removeItem('fb_liked_posts');
        likedPosts.length = 0;
        logUserAction('清除所有点赞记录');
    }

    // 清除所有链接
    function clearAllLinks() {
        // 清除待点赞列表
        localStorage.removeItem('fb_auto_like_posts');
        targetPosts.length = 0;

        // 清除已点赞记录
        localStorage.removeItem('fb_liked_posts');
        likedPosts.length = 0;

        // 清除状态
        localStorage.removeItem('fb_auto_like_state');

        logUserAction('清除所有链接和记录');
    }

    // 清除日志记录
    function clearLogs() {
        const logPanel = document.querySelector('#userLogPanel');
        if (logPanel) {
            logPanel.innerHTML = '';
        }
    }

    // 添加控制面板
    function addControlPanel() {
        // 主面板
        const panel = document.createElement('div');
        panel.style.cssText = `
            position: fixed;
            top: 50%;
            right: 20px;
            transform: translateY(-50%);
            background: white;
            padding: 15px;
            border-radius: 10px;
            z-index: 9999;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            max-width: 320px;
            font-family: Arial, sans-serif;
            transition: all 0.3s ease;
        `;

        // 标题栏
        const titleBar = document.createElement('div');
        titleBar.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        `;

        const title = document.createElement('div');
        title.textContent = 'Facebook 自动点赞';
        title.style.cssText = `
            font-weight: bold;
            font-size: 16px;
            color: #1877f2;
        `;

        // 状态指示器
        const statusIndicator = document.createElement('div');
        statusIndicator.id = 'statusIndicator';
        statusIndicator.style.cssText = `
            font-size: 12px;
            margin-top: 5px;
            padding: 4px 8px;
            border-radius: 4px;
            text-align: center;
        `;

        function updateStatusIndicator() {
            if (isRunning) {
                let statusText = '';
                let statusClass = '';

                if (isPageVisible) {
                    statusText = `运行中 (前台${isMainInstance ? '/主实例' : ''})`;
                    statusClass = 'active-foreground';
                } else {
                    statusText = `运行中 (后台${isMainInstance ? '/主实例' : ''})`;
                    statusClass = 'active-background';
                }

                // 添加待处理数量
                const pendingCount = getPendingPosts().length;
                if (pendingCount > 0) {
                    statusText += ` - 待处理: ${pendingCount}`;
                }

                statusIndicator.textContent = statusText;

                // 设置样式
                if (statusClass === 'active-foreground') {
                    statusIndicator.style.background = '#d4edda';
                    statusIndicator.style.color = '#155724';
                    statusIndicator.style.border = '1px solid #c3e6cb';
                } else {
                    statusIndicator.style.background = '#fff3cd';
                    statusIndicator.style.color = '#856404';
                    statusIndicator.style.border = '1px solid #ffeaa7';
                }
            } else {
                statusIndicator.textContent = '已停止';
                statusIndicator.style.background = '#f8d7da';
                statusIndicator.style.color = '#721c24';
                statusIndicator.style.border = '1px solid #f5c6cb';
            }
        }

        // 定期更新状态指示器
        setInterval(updateStatusIndicator, 1000);
        updateStatusIndicator();

        // 最小化按钮
        const minimizeBtn = document.createElement('button');
        minimizeBtn.innerHTML = '−';
        minimizeBtn.style.cssText = `
            background: none;
            border: none;
            font-size: 20px;
            cursor: pointer;
            color: #666;
            padding: 0 5px;
            line-height: 1;
        `;

        let isMinimized = false;
        const content = document.createElement('div');
        content.style.transition = 'all 0.3s ease';

        minimizeBtn.onclick = () => {
            isMinimized = !isMinimized;
            content.style.display = isMinimized ? 'none' : 'block';
            minimizeBtn.innerHTML = isMinimized ? '+' : '−';
            panel.style.width = isMinimized ? 'auto' : '320px';
        };

        const titleContainer = document.createElement('div');
        titleContainer.appendChild(title);
        titleContainer.appendChild(statusIndicator);

        titleBar.appendChild(titleContainer);
        titleBar.appendChild(minimizeBtn);
        panel.appendChild(titleBar);

        // 创建带标签的区域函数
        function createLabeledSection(labelText) {
            const section = document.createElement('div');
            section.style.marginBottom = '15px';

            const label = document.createElement('label');
            label.textContent = labelText;
            label.style.cssText = `
                display: block;
                margin-bottom: 5px;
                font-weight: 500;
                color: #444;
                font-size: 14px;
            `;

            section.appendChild(label);
            return section;
        }

        // 链接输入区域
        const inputSection = createLabeledSection('批量添加链接');
        const input = document.createElement('textarea');
        input.placeholder = '输入 Facebook 链接（每行一个）';
        input.style.cssText = `
            width: 100%;
            height: 80px;
            padding: 8px;
            margin-bottom: 10px;
            border: 1px solid #ddd;
            border-radius: 6px;
            resize: vertical;
            font-size: 14px;
            box-sizing: border-box;
        `;
        inputSection.appendChild(input);

        // 文件上传区域
        const uploadSection = createLabeledSection('文件上传');
        const fileInput = document.createElement('input');
        fileInput.type = 'file';
        fileInput.accept = '.txt';
        fileInput.style.display = 'none';

        const dropZone = document.createElement('div');
        dropZone.style.cssText = `
            border: 2px dashed #1877f2;
            border-radius: 8px;
            padding: 15px;
            text-align: center;
            background: #f0f2f5;
            cursor: pointer;
            transition: all 0.3s ease;
        `;
        dropZone.innerHTML = `
            <div style="color: #1877f2; margin-bottom: 5px;">
                <i class="fas fa-cloud-upload-alt" style="font-size: 20px;"></i>
            </div>
            <div style="color: #666; font-size: 13px;">
                点击或拖放文件到这里上传<br>
                <span style="font-size: 12px; color: #999;">支持 .txt 文件</span>
            </div>
        `;

        // 处理文件内容的函数
        function handleFileContent(content) {
            if (content.trim()) {
                if (addToLikeList(content)) {
                    const urls = parseFacebookUrls(content);
                    showToast(`成功从文件添加 ${urls.length} 个链接！`);
                    updatePendingList();
                }
            } else {
                showToast('文件内容为空！', 'error');
            }
        }

        // 处理文件的函数
        function handleFile(file) {
            if (file.type !== 'text/plain') {
                showToast('请上传 .txt 文件！', 'error');
                return;
            }

            const reader = new FileReader();
            reader.onload = (e) => {
                handleFileContent(e.target.result);
            };
            reader.onerror = () => {
                showToast('读取文件失败！', 'error');
            };
            reader.readAsText(file);
        }

        // 点击上传
        dropZone.onclick = () => fileInput.click();

        // 文件选择处理
        fileInput.onchange = (e) => {
            const file = e.target.files[0];
            if (file) {
                handleFile(file);
            }
            // 清除选择，这样同一个文件可以重复上传
            fileInput.value = '';
        };

        // 拖拽相关事件
        dropZone.ondragover = (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#28a745';
            dropZone.style.background = '#e8f5e9';
        };

        dropZone.ondragleave = (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#1877f2';
            dropZone.style.background = '#f0f2f5';
        };

        dropZone.ondrop = (e) => {
            e.preventDefault();
            dropZone.style.borderColor = '#1877f2';
            dropZone.style.background = '#f0f2f5';

            const file = e.dataTransfer.files[0];
            if (file) {
                handleFile(file);
            }
        };

        uploadSection.appendChild(dropZone);
        uploadSection.appendChild(fileInput);

        // 待处理链接列表区域
        const pendingSection = createLabeledSection('待处理链接');
        const pendingHeader = document.createElement('div');
        pendingHeader.style.cssText = `
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
        `;

        // 添加清除所有链接的按钮
        const clearAllButton = document.createElement('button');
        clearAllButton.textContent = '清除所有链接';
        clearAllButton.style.cssText = `
            background: none;
            border: none;
            color: #dc3545;
            cursor: pointer;
            font-size: 12px;
            padding: 2px 8px;
            border-radius: 4px;
            &:hover {
                background: #dc3545;
                color: white;
            }
        `;
        clearAllButton.onclick = () => {
            if (confirm('确定要清除所有链接吗？此操作不可恢复。')) {
                clearAllLinks();
                updatePendingList();
            }
        };

        pendingHeader.appendChild(clearAllButton);
        pendingSection.appendChild(pendingHeader);

        const pendingList = document.createElement('div');
        pendingList.style.cssText = `
            max-height: 150px;
            overflow-y: auto;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fff;
            font-size: 13px;
        `;

        // 更新待处理列表的函数
        function updatePendingList() {
            const pendingPosts = getPendingPosts();
            pendingList.innerHTML = '';
            pendingPosts.forEach(url => {
                const item = document.createElement('div');
                item.style.cssText = `
                    display: flex;
                    justify-content: space-between;
                    align-items: center;
                    padding: 5px;
                    border-bottom: 1px solid #eee;
                `;

                const urlText = document.createElement('div');
                urlText.style.cssText = `
                    flex: 1;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    white-space: nowrap;
                    margin-right: 10px;
                `;
                urlText.textContent = url;

                const deleteBtn = document.createElement('button');
                deleteBtn.innerHTML = '×';
                deleteBtn.style.cssText = `
                    background: none;
                    border: none;
                    color: #dc3545;
                    cursor: pointer;
                    font-size: 18px;
                    padding: 0 5px;
                `;
                deleteBtn.onclick = (e) => {
                    e.stopPropagation();
                    const index = targetPosts.indexOf(url);
                    if (index > -1) {
                        targetPosts.splice(index, 1);
                        localStorage.setItem('fb_auto_like_posts', JSON.stringify(targetPosts));
                        updatePendingList();
                        logUserAction(`删除链接: ${url}`);
                    }
                };

                item.appendChild(urlText);
                item.appendChild(deleteBtn);
                pendingList.appendChild(item);
            });
        }
        pendingSection.appendChild(pendingList);

        // 操作日志面板
        const logSection = createLabeledSection('操作日志');
        const logPanel = document.createElement('div');
        logPanel.id = 'userLogPanel';
        logPanel.style.cssText = `
            height: 150px;
            overflow-y: auto;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 6px;
            background: #fff;
            font-size: 13px;
            margin-bottom: 15px;
        `;
        logSection.appendChild(logPanel);

        // 按钮组
        const buttonGroup = document.createElement('div');
        buttonGroup.style.cssText = `
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 8px;
            margin-top: 10px;
        `;

        function createButton(text, bgColor, hoverColor) {
            const button = document.createElement('button');
            button.textContent = text;
            button.style.cssText = `
                padding: 8px 15px;
                border: none;
                border-radius: 6px;
                background: ${bgColor};
                color: white;
                cursor: pointer;
                font-size: 14px;
                transition: background 0.3s;
                &:hover {
                    background: ${hoverColor};
                }
            `;
            return button;
        }

        const startButton = createButton('开始点赞', '#1877f2', '#0d65d9');
        const stopButton = createButton('停止点赞', '#dc3545', '#c82333');
        const clearLogsButton = createButton('清除日志', '#6c757d', '#5a6268');
        const addButton = createButton('添加链接', '#28a745', '#218838');

        startButton.onclick = startAutoLike;
        stopButton.onclick = stopAutoLike;
        clearLogsButton.onclick = () => {
            if (confirm('确定要清除所有日志记录吗？')) {
                clearLogs();
            }
        };
        addButton.onclick = () => {
            if (addToLikeList(input.value)) {
                const urls = parseFacebookUrls(input.value);
                showToast(`成功添加 ${urls.length} 个链接！`);
                input.value = '';
                updatePendingList();
            }
        };

        // 添加所有元素到内容区
        content.appendChild(inputSection);
        content.appendChild(uploadSection);
        content.appendChild(pendingSection);
        content.appendChild(logSection);
        buttonGroup.appendChild(startButton);
        buttonGroup.appendChild(stopButton);
        buttonGroup.appendChild(clearLogsButton);
        buttonGroup.appendChild(addButton);
        content.appendChild(buttonGroup);

        panel.appendChild(content);
        document.body.appendChild(panel);

        // 初始化显示
        updatePendingList();
    }

    // 添加提示框功能
    function showToast(message, type = 'success') {
        const toast = document.createElement('div');
        toast.style.cssText = `
            position: fixed;
            bottom: 20px;
            right: 20px;
            padding: 12px 24px;
            background: ${type === 'success' ? '#4caf50' : '#f44336'};
            color: white;
            border-radius: 4px;
            font-size: 14px;
            z-index: 10000;
            animation: slideIn 0.3s ease;
        `;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => toast.remove(), 300);
        }, 3000);
    }

    // 添加必要的动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideIn {
            from { transform: translateX(100%); opacity: 0; }
            to { transform: translateX(0); opacity: 1; }
        }
        @keyframes slideOut {
            from { transform: translateX(0); opacity: 1; }
            to { transform: translateX(100%); opacity: 0; }
        }
    `;
    document.head.appendChild(style);

    // 心跳机制，确保脚本在后台持续运行
    let heartbeatTimer = null;

    function startHeartbeat() {
        if (heartbeatTimer) return;

        heartbeatTimer = createRobustTimer(() => {
            if (isRunning) {
                lastActivityTime = Date.now();
                saveCurrentState(isRunning, currentOriginalUrl, currentFinalUrl);

                // 检查是否有任务需要处理但长时间没有活动
                const timeSinceLastActivity = Date.now() - lastActivityTime;
                if (timeSinceLastActivity > 60000) { // 超过1分钟没有活动
                    logUserAction('检测到长时间无活动，检查任务状态');
                    checkTaskContinuity();
                }
            }
        }, 10000); // 每10秒心跳一次
    }

    function stopHeartbeat() {
        if (heartbeatTimer) {
            heartbeatTimer.clear();
            heartbeatTimer = null;
        }
    }

    // 页面加载时检查是否需要继续任务
    window.addEventListener('load', function() {
        logUserAction(`页面加载完成 - 运行状态: ${isRunning}, 页面可见: ${isPageVisible}`);

        if (isRunning) {
            startHeartbeat(); // 启动心跳机制

            const currentUrl = window.location.href;

            if (currentOriginalUrl) {
                // 如果有正在处理的原始URL
                if (!currentFinalUrl) {
                    // 如果还没有记录最终URL，说明这是重定向后的页面
                    logUserAction(`处理重定向后的页面: ${currentUrl}`);
                    checkAndLike(currentOriginalUrl);
                } else if (currentUrl === currentFinalUrl) {
                    // 如果当前URL与记录的最终URL匹配
                    logUserAction(`继续处理当前页面: ${currentUrl}`);
                    checkAndLike(currentOriginalUrl);
                } else {
                    // URL不匹配，可能是用户手动导航，重新开始处理
                    logUserAction('URL不匹配，重新开始处理');
                    performLike();
                }
            } else {
                // 没有正在处理的URL，开始处理新的链接
                logUserAction('开始处理新的链接');
                performLike();
            }
        }
    });

    // 初始化时如果正在运行，则继续执行
    if (isRunning) {
        window.addEventListener('load', function() {
            logUserAction('恢复自动点赞任务');
            const currentUrl = window.location.href;

            // 启动保活机制
            startKeepAlive();
            startHeartbeat();

            // 显示通知
            if (typeof GM_notification !== 'undefined') {
                GM_notification({
                    title: 'Facebook 自动点赞',
                    text: '已恢复自动点赞任务 (支持后台运行)',
                    timeout: 3000
                });
            }

            checkAndLike(currentUrl);
        });
    }

    // 添加Tampermonkey菜单命令
    if (typeof GM_registerMenuCommand !== 'undefined') {
        GM_registerMenuCommand('启动自动点赞', function() {
            startAutoLike();
        });

        GM_registerMenuCommand('停止自动点赞', function() {
            stopAutoLike();
        });

        GM_registerMenuCommand('查看状态', function() {
            const status = {
                isRunning: isRunning,
                isPageVisible: isPageVisible,
                isMainInstance: isMainInstance,
                instanceId: instanceId,
                pendingCount: getPendingPosts().length,
                likedCount: likedPosts.length,
                lastActivity: new Date(lastActivityTime).toLocaleString()
            };

            alert(`状态信息:\n` +
                  `运行状态: ${status.isRunning ? '运行中' : '已停止'}\n` +
                  `页面可见: ${status.isPageVisible ? '是' : '否'}\n` +
                  `主实例: ${status.isMainInstance ? '是' : '否'}\n` +
                  `实例ID: ${status.instanceId}\n` +
                  `待处理数量: ${status.pendingCount}\n` +
                  `已点赞数量: ${status.likedCount}\n` +
                  `最后活动: ${status.lastActivity}`);
        });
    }

    // 初始化
    window.addEventListener('load', function() {
        // 初始化跨标签页通信
        initCrossTabCommunication();

        // 添加控制面板
        addControlPanel();

        // 如果正在运行，启动保活机制
        if (isRunning) {
            startKeepAlive();
        }

        console.log(`Facebook Auto Liker ${SCRIPT_VERSION} 已启动 - 实例ID: ${instanceId}`);
    });
  })();
















































