// ==UserScript==
// @name         Image Link Downloader
// @namespace    http://tampermonkey.net/
// @version      0.1
// @description  Download images from <a> tags with custom naming
// <AUTHOR> Name
// @match        *://*.dianping.com/*
// @match        *://*.meituan.com/*
// @grant        GM_download
// @grant        GM_setValue
// @grant        GM_getValue
// @grant        GM_xmlhttpRequest
// @require      https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js
// @require      https://cdnjs.cloudflare.com/ajax/libs/jszip/3.7.1/jszip.min.js
// @connect      *
// ==/UserScript==

(function() {
  'use strict';

  // 檢查是否為目標域名
  function isTargetDomain() {
      const hostname = window.location.hostname;
      return hostname.includes('dianping.com') || hostname.includes('meituan.com');
  }

  if (!isTargetDomain()) return;

  // 控制面板樣式
  const styles = `
      .control-panel {
          position: fixed;
          top: 20px;
          right: 20px;
          background: white;
          padding: 15px;
          border-radius: 8px;
          box-shadow: 0 2px 10px rgba(0,0,0,0.1);
          z-index: 9999;
          width: 300px;
          transition: all 0.3s ease;
      }
      .control-panel.minimized {
          width: 40px;
          height: 40px;
          overflow: hidden;
          padding: 8px;
      }
      .control-panel input {
          width: 100%;
          padding: 8px;
          margin-bottom: 10px;
          border: 1px solid #ddd;
          border-radius: 4px;
      }
      .control-panel button {
          padding: 8px 15px;
          background: #4CAF50;
          color: white;
          border: none;
          border-radius: 4px;
          cursor: pointer;
          margin-right: 10px;
          margin-bottom: 10px;
      }
      .control-panel button:hover {
          background: #45a049;
      }
      .log-area {
          margin-top: 10px;
          max-height: 150px;
          overflow-y: auto;
          border: 1px solid #ddd;
          padding: 8px;
          font-size: 12px;
      }
      .panel-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
      }
      .minimize-btn {
          background: #f0f0f0 !important;
          color: #333 !important;
          padding: 4px 8px !important;
          margin: 0 !important;
      }
      .expand-btn {
          width: 100%;
          height: 100%;
          padding: 0 !important;
          margin: 0 !important;
          background: #4CAF50 !important;
          display: none;
      }
      .control-panel.minimized .expand-btn {
          display: block;
      }
      .control-panel.minimized .panel-content {
          display: none;
      }
  `;

  // 添加樣式
  const styleSheet = document.createElement('style');
  styleSheet.textContent = styles;
  document.head.appendChild(styleSheet);

  // 獲取 meta keywords
  function getMetaKeywords() {
      const metaKeywords = document.querySelector('meta[name="keywords"]');
      return metaKeywords ? metaKeywords.getAttribute('content').trim() : '';
  }

  // 創建控制面板
  function createControlPanel() {
      const panel = document.createElement('div');
      panel.className = 'control-panel';
      panel.innerHTML = `
          <button class="expand-btn" id="expandBtn">展開</button>
          <div class="panel-content">
              <div class="panel-header">
                  <h3 style="margin: 0;">圖片下載器</h3>
                  <button class="minimize-btn" id="minimizeBtn">─</button>
              </div>
              <div style="margin-bottom: 10px;">
                  <label for="savePath">保存路徑：</label>
                  <input type="text" id="savePath" placeholder="例如：C:\\Downloads\\Images" style="width: 100%;">
              </div>
              <button id="startDownload">開始下載</button>
              <button id="clearLog">清除日誌</button>
              <div class="log-area" id="logArea"></div>
          </div>
      `;
      document.body.appendChild(panel);

      // 恢復上次使用的路徑
      const lastPath = GM_getValue('lastSavePath', '');
      if (lastPath) {
          document.getElementById('savePath').value = lastPath;
      }

      // 綁定最小化和展開按鈕事件
      const minimizeBtn = document.getElementById('minimizeBtn');
      const expandBtn = document.getElementById('expandBtn');

      minimizeBtn.addEventListener('click', () => {
          panel.classList.add('minimized');
      });

      expandBtn.addEventListener('click', () => {
          panel.classList.remove('minimized');
      });
  }

  // 日誌功能
  function log(message) {
      const logArea = document.getElementById('logArea');
      if (logArea) {
          const logEntry = document.createElement('div');
          logEntry.textContent = `${new Date().toLocaleTimeString()} - ${message}`;
          logArea.appendChild(logEntry);
          logArea.scrollTop = logArea.scrollHeight;
      }
  }

  // 清理圖片URL
  function cleanImageUrl(url) {
      const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp', '.bmp'];
      let lastIndex = -1;
      imageExtensions.forEach(ext => {
          const index = url.toLowerCase().indexOf(ext);
          if (index !== -1 && (lastIndex === -1 || index > lastIndex)) {
              lastIndex = index + ext.length;
          }
      });

      if (lastIndex !== -1) {
          return url.substring(0, lastIndex);
      }
      return url;
  }

  // 下載圖片
  async function downloadImage(imageUrl, savePath, index) {
      try {
          // 獲取文件擴展名
          const extension = imageUrl.split('.').pop().toLowerCase();
          const keywords = getMetaKeywords() || 'image';
          const filename = `${keywords}_${index}.${extension}`;

          // 使用 GM_download 直接下載到指定路徑
          GM_download({
              url: imageUrl,
              name: filename,
              saveAs: false,  // 設置為 false 以使用指定的文件名直接保存
              headers: {
                  "Referer": window.location.href
              },
              onload: () => {
                  log(`成功保存: ${filename} 到 ${savePath}`);
              },
              onerror: (error) => {
                  log(`下載失敗 ${filename}: ${error.error || '未知錯誤'}`);
              }
          });

          // 添加小延遲以避免同時下載太多
          await new Promise(resolve => setTimeout(resolve, 500));
      } catch (error) {
          log(`下載失敗 ${imageUrl}: ${error.message}`);
      }
  }

  // 主要處理函數
  async function processImages() {
      const savePath = document.getElementById('savePath').value.trim();

      if (!savePath) {
          alert('請輸入保存路徑');
          return;
      }

      // 保存路徑設置
      GM_setValue('lastSavePath', savePath);

      // 請求文件系統權限
      try {
          // 移除 startIn 參數，讓用戶自行選擇目錄
          const dirHandle = await window.showDirectoryPicker();

          const links = document.getElementsByTagName('a');
          let imageCount = 0;

          for (let i = 0; i < links.length; i++) {
              const link = links[i];
              const images = link.getElementsByTagName('img');

              if (images.length > 0) {
                  const img = images[0];
                  const imageUrl = img.src || img.dataset.src;

                  if (imageUrl) {
                      imageCount++;
                      try {
                          const response = await fetch(imageUrl);
                          const blob = await response.blob();

                          // 獲取文件擴展名
                          const extension = imageUrl.split('.').pop().toLowerCase();
                          const keywords = getMetaKeywords() || 'image';
                          const filename = `${keywords}_${imageCount}.${extension}`;

                          // 在選定的目錄中創建文件
                          const fileHandle = await dirHandle.getFileHandle(filename, { create: true });
                          const writable = await fileHandle.createWritable();
                          await writable.write(blob);
                          await writable.close();

                          log(`成功保存: ${filename}`);
                      } catch (error) {
                          log(`下載失敗 ${imageUrl}: ${error.message}`);
                      }
                  }
              }
          }

          log(`處理完成：共下載 ${imageCount} 個圖片`);
      } catch (err) {
          if (err.name === 'NotAllowedError') {
              alert('需要授予文件夾訪問權限才能繼續。請選擇保存位置並允許訪問。');
          } else {
              log(`錯誤：${err.message}`);
              // 降級方案：使用普通下載
              alert('無法使用高級保存功能，將使用普通下載模式');
              await downloadImagesNormal();
          }
      }
  }

  // 降級方案：普通下載模式
  async function downloadImagesNormal() {
      const links = document.getElementsByTagName('a');
      let imageCount = 0;

      for (let i = 0; i < links.length; i++) {
          const link = links[i];
          const images = link.getElementsByTagName('img');

          if (images.length > 0) {
              const img = images[0];
              const imageUrl = img.src || img.dataset.src;

              if (imageUrl) {
                  imageCount++;
                  try {
                      const response = await fetch(imageUrl);
                      const blob = await response.blob();

                      const extension = imageUrl.split('.').pop().toLowerCase();
                      const keywords = getMetaKeywords() || 'image';
                      const filename = `${keywords}_${imageCount}.${extension}`;

                      saveAs(blob, filename);
                      log(`已下載: ${filename}`);
                  } catch (error) {
                      log(`下載失敗 ${imageUrl}: ${error.message}`);
                  }
              }
          }
      }
  }

  // 初始化
  function init() {
      createControlPanel();

      // 綁定事件
      document.getElementById('startDownload').addEventListener('click', processImages);
      document.getElementById('clearLog').addEventListener('click', () => {
          document.getElementById('logArea').innerHTML = '';
      });
  }

  // 等待頁面加載完成
  window.addEventListener('load', init);
})();
